'use client';

import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { CheckIcon, StarIcon, XMarkIcon, BoltIcon, ShieldCheckIcon, SparklesIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import Landing<PERSON><PERSON>bar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';


const pricingTiers = [
  {
    name: "Free",
    price: 0,
    description: "Perfect for getting started and testing RouKey",
    features: [
      "Unlimited API requests",
      "1 Custom Configuration",
      "3 API Keys per config",
      "All 300+ AI models",
      "Strict fallback routing only",
      "Basic analytics only",
      "No custom roles, basic router only",
      "Limited logs",
      "Community support"
    ],
    notIncluded: [
      "Advanced routing strategies",
      "Custom roles",
      "Prompt engineering",
      "Knowledge base",
      "Semantic caching"
    ],
    cta: "Start free trial",
    popular: false,
    icon: BoltIcon,
    gradient: "from-blue-500 to-purple-600"
  },
  {
    name: "Starter",
    price: 24,
    description: "Perfect for individual developers and small projects",
    features: [
      "Unlimited API requests",
      "5 Custom Configurations",
      "5 API Keys per config",
      "All 300+ AI models",
      "Strict fallback + Complex routing (1 config limit)",
      "Up to 3 custom roles",
      "Intelligent role routing (1 config)",
      "Prompt engineering (no file upload)",
      "Enhanced logs and analytics",
      "Community support"
    ],
    notIncluded: [
      "Knowledge base",
      "Semantic caching",
      "Priority support"
    ],
    cta: "Get Started",
    popular: false,
    icon: CpuChipIcon,
    gradient: "from-orange-500 to-orange-600"
  },
  {
    name: "Professional",
    price: 60,
    description: "Ideal for growing businesses and development teams",
    features: [
      "Unlimited API requests",
      "20 Custom Configurations",
      "15 API Keys per config",
      "All 300+ AI models",
      "All advanced routing strategies",
      "Unlimited custom roles",
      "Prompt engineering + Knowledge base (5 documents)",
      "Semantic caching",
      "Advanced analytics and logging",
      "Priority email support"
    ],
    notIncluded: [
      "Custom integrations",
      "Phone support",
      "SLA guarantee"
    ],
    cta: "Get Started",
    popular: true,
    icon: StarIcon,
    gradient: "from-purple-500 to-purple-600"
  },
  {
    name: "Enterprise",
    price: 170,
    description: "For large organizations with advanced AI routing needs",
    features: [
      "Unlimited API requests",
      "Unlimited configurations",
      "Unlimited API keys",
      "All 300+ models + priority access",
      "All routing strategies",
      "Unlimited custom roles",
      "All features + priority support",
      "Unlimited knowledge base documents",
      "Advanced semantic caching",
      "Custom integrations",
      "Dedicated support + phone",
      "SLA guarantee"
    ],
    notIncluded: [],
    cta: "Contact us",
    popular: false,
    icon: BuildingOfficeIcon,
    gradient: "from-emerald-500 to-emerald-600"
  }
];

const comparisonFeatures = [
  {
    category: "Core features",
    features: [
      { name: "Leading-edge UI", free: "✓", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "All integrations", free: "✓", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "Streaming and bulk operations", free: "✓", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "Easily merge data", free: "✓", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "Workflow templates", free: "✓", starter: "✓", pro: "✓", enterprise: "✓" },
      { name: "Workflow history", free: "Up to 1 day", starter: "Up to 5 days", pro: "Up to 1 year", enterprise: "Up to 1 year" }
    ]
  },
  {
    category: "Developer tools",
    features: [
      { name: "API Requests per month", free: "Unlimited", starter: "Unlimited", pro: "Unlimited", enterprise: "Unlimited" },
      { name: "Custom Configurations", free: "1", starter: "4", pro: "20", enterprise: "Unlimited" },
      { name: "API Keys per config", free: "3", starter: "50", pro: "Unlimited", enterprise: "Unlimited" },
      { name: "Supported AI Models", free: "300+", starter: "300+", pro: "300+", enterprise: "300+ + Priority" },
      { name: "Routing Strategies", free: "Strict fallback only", starter: "Strict + Complex (1 config)", pro: "All strategies", enterprise: "All strategies" },
      { name: "Custom Roles", free: "✗", starter: "Up to 3", pro: "Unlimited", enterprise: "Unlimited" },
      { name: "Intelligent Role Routing", free: "✗", starter: "✓ (1 config)", pro: "✓", enterprise: "✓" }
    ]
  },
  {
    category: "Advanced features",
    features: [
      { name: "Prompt Engineering", free: "✗", starter: "✓ (no upload)", pro: "✓ + Knowledge base", enterprise: "Advanced" },
      { name: "Knowledge Base Documents", free: "✗", starter: "✗", pro: "5 documents", enterprise: "15 documents" },
      { name: "Semantic Caching", free: "✗", starter: "✗", pro: "✓", enterprise: "Advanced" },
      { name: "Custom Integrations", free: "✗", starter: "✗", pro: "✗", enterprise: "✓" },
      { name: "Analytics & Logs", free: "Basic only", starter: "Enhanced", pro: "Advanced", enterprise: "Enterprise" },
      { name: "Support Level", free: "Community", starter: "Community", pro: "Priority Email", enterprise: "Dedicated + Phone" },
      { name: "SLA Guarantee", free: "✗", starter: "✗", pro: "✗", enterprise: "✓" }
    ]
  }
];

// Helper function to render feature values with proper styling
const renderFeatureValue = (value: string) => {
  if (value === "✓") {
    return <CheckIcon className="h-5 w-5 text-green-400 mx-auto" />;
  }
  if (value === "✗") {
    return <XMarkIcon className="h-5 w-5 text-red-400 mx-auto" />;
  }
  return <span className="text-gray-300">{value}</span>;
};

function PricingPageContent() {
  // Check for subscription required message
  const urlParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null;
  const message = urlParams?.get('message');
  const plan = urlParams?.get('plan');
  const showSubscriptionRequired = message === 'subscription_required' || message === 'subscription_check_failed';
  const showCompletePayment = message === 'complete_payment';

  return (
    <div className="min-h-screen" style={{
      background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
    }}>
      <LandingNavbar />

      {/* Subscription Required Alert */}
      {showSubscriptionRequired && (
        <div className="bg-[#ff6b35] text-white py-3 px-4 text-center">
          <p className="text-sm font-medium">
            🔒 Active subscription required to access the dashboard. Please choose a plan below to continue.
          </p>
        </div>
      )}

      {/* Complete Payment Alert */}
      {showCompletePayment && (
        <div className="bg-amber-500 text-white py-3 px-4 text-center">
          <p className="text-sm font-medium">
            ⚠️ Please complete your payment to activate your {plan ? plan.charAt(0).toUpperCase() + plan.slice(1) : ''} plan. Your account is currently pending payment.
          </p>
        </div>
      )}

      <main className="pt-20 relative">
        {/* Enhanced Grid Background */}
        <EnhancedGridBackground
          gridSize={50}
          opacity={0.03}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="fixed inset-0"
        />

        {/* Hero Section */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6"
            >
              Universal AI access with
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                your own keys
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
            >
              Choose the plan that fits your needs. All plans include intelligent routing to 300+ AI models with complete cost transparency.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex items-center justify-center gap-6 mb-12"
            >
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <span className="text-white text-sm">Monthly</span>
              </div>
              <div className="flex items-center gap-2 opacity-60">
                <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                <span className="text-white/60 text-sm">Annually</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="pb-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingTiers.slice(0, 3).map((tier, index) => {
                const IconComponent = tier.icon;
                return (
                  <motion.div
                    key={tier.name}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className={`relative rounded-3xl p-8 backdrop-blur-sm border ${
                      tier.popular
                        ? 'bg-white/10 border-orange-500/50 shadow-2xl shadow-orange-500/20'
                        : 'bg-white/5 border-white/10 hover:bg-white/10'
                    } transition-all duration-300 hover:transform hover:scale-105`}
                  >
                    {tier.popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                          <StarIcon className="h-4 w-4 mr-1" />
                          Pro
                        </div>
                      </div>
                    )}

                    <div className="text-center mb-8">
                      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${tier.gradient} mb-4`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                      <p className="text-gray-300 mb-6 text-sm">{tier.description}</p>
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-white">${tier.price}</span>
                        <span className="text-gray-400 ml-2">per month, billed monthly</span>
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">
                          {tier.name === 'Free' ? 'Unlimited' : tier.name === 'Starter' ? 'Unlimited' : 'Unlimited'}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {tier.name === 'Free' ? 'API requests to 300+ models' : tier.name === 'Starter' ? 'API requests to 300+ models' : 'API requests to 300+ models'}
                        </div>
                        <div className="text-gray-400 text-xs mt-1">
                          {tier.name === 'Free' ? 'with your own API keys' : tier.name === 'Starter' ? 'with intelligent routing' : 'with advanced orchestration'}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Configurations:</span>
                          <span className="text-white">{tier.name === 'Free' ? '1' : tier.name === 'Starter' ? '5' : tier.name === 'Professional' ? '20' : 'Unlimited'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">API Keys per config:</span>
                          <span className="text-white">{tier.name === 'Free' ? '3' : tier.name === 'Starter' ? '5' : tier.name === 'Professional' ? '15' : 'Unlimited'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Custom Roles:</span>
                          <span className="text-white">{tier.name === 'Free' ? 'None' : tier.name === 'Starter' ? 'Up to 3' : 'Unlimited'}</span>
                        </div>
                      </div>
                    </div>

                    <Link
                      href={tier.name === 'Enterprise' ? '/contact' : tier.name === 'Free' ? '/auth/signup?plan=free' : `/auth/signup?plan=${tier.name.toLowerCase()}`}
                      className={`block w-full text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 ${
                        tier.name === 'Enterprise'
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl'
                          : tier.popular
                          ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 shadow-lg hover:shadow-xl'
                          : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {tier.cta}
                    </Link>
                  </motion.div>
                );
              })}
            </div>

            {/* Enterprise Card - Separate */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mt-12 max-w-4xl mx-auto"
            >
              <div className="relative rounded-3xl p-8 bg-gradient-to-r from-purple-900/20 to-blue-900/20 backdrop-blur-sm border border-purple-500/30">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-blue-600 mb-4">
                    <StarIcon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2">Enterprise</h3>
                  <p className="text-gray-300 mb-6 text-sm">For large organizations with advanced AI routing needs</p>
                  <div className="flex items-baseline justify-center mb-8">
                    <span className="text-4xl font-bold text-white">$170</span>
                    <span className="text-gray-400 ml-2">per month, billed monthly</span>
                  </div>

                  <div className="text-center mb-8">
                    <div className="text-2xl font-bold text-white mb-1">Unlimited</div>
                    <div className="text-gray-400 text-sm">Everything + priority support</div>
                    <div className="text-gray-400 text-xs mt-1">with dedicated infrastructure</div>
                  </div>

                  <Link
                    href="/contact"
                    className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    Contact us
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Looking for something else section */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Looking for something else?</h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">BYOK Framework</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Bring Your Own Keys - maintain complete control over your API costs.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  RouKey never marks up your API costs. You pay providers directly
                  while getting intelligent routing and cost optimization features.
                </p>
                <Link
                  href="/docs"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  Learn more
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">Multi-Agent Workflows</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Advanced orchestration with LangGraph.js for complex AI tasks.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  Sequential, parallel, and hierarchical workflows with memory
                  persistence and real-time streaming.
                </p>
                <Link
                  href="/features"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  Explore features
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">Smart Cost Optimization</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Save up to 70% on AI costs with intelligent routing strategies.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  Route simple tasks to cheaper models and complex tasks to
                  premium models automatically.
                </p>
                <Link
                  href="/routing-strategies"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  View strategies
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Feature Comparison Table */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-white mb-4">What's included?</h2>
            </motion.div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-white/5">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-white">Features</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white">Starter</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white bg-orange-500/10">Pro</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white">Enterprise</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/10">
                    {comparisonFeatures.map((category, categoryIndex) => (
                      <React.Fragment key={category.category}>
                        <tr className="bg-white/5">
                          <td colSpan={4} className="px-6 py-3 text-sm font-semibold text-white">
                            {category.category}
                          </td>
                        </tr>
                        {category.features.map((feature, featureIndex) => (
                          <tr key={`${categoryIndex}-${featureIndex}`} className="hover:bg-white/5">
                            <td className="px-6 py-4 text-sm text-gray-300">{feature.name}</td>
                            <td className="px-6 py-4 text-sm text-center">{renderFeatureValue(feature.starter)}</td>
                            <td className="px-6 py-4 text-sm text-center bg-orange-500/5">{renderFeatureValue(feature.pro)}</td>
                            <td className="px-6 py-4 text-sm text-center">{renderFeatureValue(feature.enterprise)}</td>
                          </tr>
                        ))}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 relative">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-left mb-16"
            >
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 rounded bg-orange-500/20 flex items-center justify-center">
                  <span className="text-orange-400 text-xs font-bold">?</span>
                </div>
                <span className="text-orange-400 text-sm font-medium">FAQs</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Frequently asked questions</h2>
            </motion.div>

            <div className="space-y-4">
              {[
                {
                  question: "How does RouKey's BYOK model work?",
                  answer: "With Bring Your Own Keys (BYOK), you provide your own API keys from providers like OpenAI, Anthropic, Google, etc. RouKey intelligently routes your requests to the optimal model while you pay providers directly. No markup, complete cost transparency, and you maintain full control over your API spending."
                },
                {
                  question: "What makes RouKey different from other AI gateways?",
                  answer: "RouKey is the only AI gateway with intelligent multi-agent orchestration, proprietary AI classification for role-based routing, and advanced cost optimization. Unlike simple proxies, RouKey uses LangGraph.js for complex workflows and can save up to 70% on AI costs through smart routing strategies."
                },
                {
                  question: "Can I cancel my subscription at any time?",
                  answer: "Yes, you can cancel your RouKey subscription at any time through your account settings. Your access will continue until the end of your current billing period, and you won't be charged for the next cycle. Your configurations and API keys remain accessible during the billing period."
                },
                {
                  question: "How secure are my API keys with RouKey?",
                  answer: "Your API keys are encrypted using enterprise-grade AES-256 encryption and stored securely. RouKey follows a BYOK model, meaning you maintain control of your keys. We never store or log your actual AI responses, and all communications are encrypted in transit with SOC 2 compliance."
                },
                {
                  question: "What routing strategies does RouKey support?",
                  answer: "RouKey offers multiple intelligent routing strategies: Intelligent Role Routing (AI-powered classification), Complexity-Based Routing (cost optimization), Strict Fallback (ordered failover), Smart Cost Optimization, and A/B Testing. Each strategy is designed for different use cases and optimization goals."
                },
                {
                  question: "Do I need technical knowledge to use RouKey?",
                  answer: "RouKey is designed for developers but offers different complexity levels. The Free plan provides simple fallback routing that requires minimal setup. Advanced features like multi-agent workflows and custom roles are available for teams that need sophisticated AI orchestration."
                },
                {
                  question: "What's included in the multi-agent workflows?",
                  answer: "RouKey's multi-agent workflows use LangGraph.js for advanced orchestration including sequential workflows, parallel execution, supervisor patterns, and hierarchical coordination. Features include memory persistence, real-time streaming, tool calling, and comprehensive error handling for complex AI tasks."
                },
                {
                  question: "How does RouKey handle rate limits and failures?",
                  answer: "RouKey automatically handles rate limits and API failures through intelligent fallback mechanisms. When one provider hits limits or fails, RouKey seamlessly routes to alternative models. This ensures high availability and reduces the impact of individual provider issues on your applications."
                }
              ].map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                    <div className="text-orange-400 text-xl font-light">+</div>
                  </div>
                  <p className="text-gray-300 text-sm mt-4 leading-relaxed">{faq.answer}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

export default function PricingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-300">Loading pricing...</p>
        </div>
      </div>
    }>
      <PricingPageContent />
    </Suspense>
  );
}

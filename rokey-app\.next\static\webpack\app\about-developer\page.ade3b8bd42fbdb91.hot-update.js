"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about-developer/page",{

/***/ "(app-pages-browser)/./src/app/about-developer/page.tsx":
/*!******************************************!*\
  !*** ./src/app/about-developer/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutDeveloperPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst achievements = [\n    {\n        label: \"Years of Development\",\n        value: \"8+\"\n    },\n    {\n        label: \"AI Models Supported\",\n        value: \"300+\"\n    },\n    {\n        label: \"API Requests Processed\",\n        value: \"10M+\"\n    },\n    {\n        label: \"Developers Trust RouKey\",\n        value: \"5,000+\"\n    }\n];\nconst principles = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Save time and money\",\n        description: \"with every AI request, and focus on building amazing products instead of managing API costs.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Avoid repetitive tasks\",\n        description: \"Complex routing logic, rate limit handling, provider switching. No more infrastructure headaches.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Get unlimited access\",\n        description: \"The more you can test and iterate, the faster you can build and ship. RouKey removes all limits.\"\n    }\n];\nfunction AboutDeveloperPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 45,\n                opacity: 0.06,\n                color: \"#ff6b35\",\n                variant: \"premium\",\n                animated: true,\n                className: \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 opacity-20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundImage: \"\\n                          linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                          linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                        \",\n                                                            backgroundSize: '20px 20px'\n                                                        },\n                                                        className: \"w-full h-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl border-4 border-white/20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: \"/founder.jpg\",\n                                                                alt: \"Okoro David Chukwunyerem - RouKey Founder\",\n                                                                className: \"w-full h-full object-cover\",\n                                                                loading: \"lazy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-6 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-block bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-white font-bold text-lg\",\n                                                                    children: \"HEY, I'M OKORO DAVID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 97,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl md:text-6xl font-bold text-white mb-6\",\n                                                    children: \"About Me\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6 text-lg text-gray-300 leading-relaxed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"In 2023, I was deep into game development, building AI-powered experiences that required massive amounts of API calls. My monthly bills were hitting \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"$500+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 172\n                                                                }, this),\n                                                                ', I was constantly hitting rate limits, and the \"smart\" routing tools I tried kept failing me.'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"After building \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"multiple games and apps\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 38\n                                                                }, this),\n                                                                \" as a developer, I realized that the biggest bottleneck wasn't creativity or coding skills—it was \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"AI infrastructure costs and limitations\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 209\n                                                                }, this),\n                                                                \". Every developer I knew was facing the same problem: \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"choose between going broke or building slowly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 348\n                                                                }, this),\n                                                                \".\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"That's when I had my breakthrough - \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"intelligent routing between multiple free trial API keys\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 59\n                                                                }, this),\n                                                                \" could give essentially unlimited testing access. \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"No more $500 monthly bills for development work!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 215\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"That's why I took my \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"8+ years of development experience\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 44\n                                                                }, this),\n                                                                \" and built \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"RouKey\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 139\n                                                                }, this),\n                                                                \" - the AI gateway I wished existed. Six months later, thousands of developers are testing fearlessly and building faster than ever. Why? Because with RouKey, you can:\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: principles.map((principle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(principle.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-white mb-3\",\n                                                            children: [\n                                                                index + 1,\n                                                                \". \",\n                                                                principle.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed\",\n                                                            children: principle.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, principle.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-300 leading-relaxed mb-8\",\n                                        children: [\n                                            \"I built RouKey because I was tired of choosing between going broke or building slowly. \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] font-bold\",\n                                                children: \"Now you can test fearlessly and build without limits!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 104\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/pricing\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer\",\n                                                children: \"Start Building with RouKey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutDeveloperPage;\nvar _c;\n$RefreshReg$(_c, \"AboutDeveloperPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about-developer/page.tsx\n"));

/***/ })

});
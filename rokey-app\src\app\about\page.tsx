'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  BoltIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  LightBulbIcon,
  CpuChipIcon,
  RocketLaunchIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Landing<PERSON><PERSON>bar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';

const stats = [
  { label: "AI Models Supported", value: "300+" },
  { label: "API Requests Processed", value: "10M+" },
  { label: "Developers Trust Us", value: "5,000+" },
  { label: "Uptime Guarantee", value: "99.9%" }
];

const features = [
  {
    icon: CpuChipIcon,
    title: "Intelligent Routing",
    description: "Smart algorithms automatically route requests to the optimal AI model based on context, role, and performance requirements."
  },
  {
    icon: BoltIcon,
    title: "Unlimited Requests",
    description: "No rate limits, no usage caps. Build and test fearlessly with unlimited access to 300+ AI models."
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Bank-grade encryption, secure key management, and compliance-ready infrastructure you can trust."
  },
  {
    icon: ChartBarIcon,
    title: "Advanced Analytics",
    description: "Deep insights into usage patterns, cost optimization, and performance metrics across all your AI requests."
  },
  {
    icon: RocketLaunchIcon,
    title: "Instant Failover",
    description: "Automatic fallback between providers ensures your applications never go down due to API outages."
  },
  {
    icon: UserGroupIcon,
    title: "Role-Based Routing",
    description: "Define custom roles and let RouKey intelligently route coding, writing, analysis, and other tasks to specialized models."
  }
];



export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      <LandingNavbar />

      {/* Enhanced Grid Background */}
      <EnhancedGridBackground
        gridSize={45}
        opacity={0.06}
        color="#ff6b35"
        variant="premium"
        animated={true}
        className="absolute inset-0"
      />

      <main className="pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h1 className="text-5xl md:text-7xl font-bold text-white mb-8">
                The Future of
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block">
                  AI Infrastructure
                </span>
              </h1>
              <p className="text-xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed">
                RouKey is the world&apos;s first truly intelligent AI gateway. We don&apos;t just route requests - we intelligently match every request to the perfect AI model, giving developers unlimited access to 300+ models with zero headaches.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link href="/pricing">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-8 py-4 rounded-2xl font-bold text-lg hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer"
                  >
                    Start Building Today
                  </motion.div>
                </Link>
                <Link href="/about-developer">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-2xl font-bold text-lg border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer"
                  >
                    Meet the Developer
                  </motion.div>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="text-center group"
                >
                  <div className="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="text-gray-300 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Why RouKey is Different
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                We&apos;re not just another API router. RouKey is the first truly intelligent AI gateway that thinks before it routes.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <feature.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="text-center"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
                Our Mission
              </h2>
              <p className="text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-12">
                To eliminate the barriers between developers and AI innovation. Every developer should have unlimited access to the world&apos;s best AI models without worrying about costs, rate limits, or complex infrastructure.
              </p>

              <div className="bg-gradient-to-r from-[#ff6b35]/20 to-[#f7931e]/20 backdrop-blur-sm border border-[#ff6b35]/30 rounded-3xl p-12">
                <blockquote className="text-2xl md:text-3xl font-bold text-white mb-6">
                  &quot;The future belongs to developers who can build without limits&quot;
                </blockquote>
                <p className="text-lg text-gray-300">
                  RouKey is more than a gateway - it&apos;s the foundation for the next generation of AI-powered applications.
                </p>
              </div>
            </motion.div>
          </div>
        </section>



        {/* CTA Section */}
        <section className="py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
          {/* Background effects */}
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style={{ animationDelay: '2s' }}></div>
          </div>

          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                Ready to Build
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block">
                  Without Limits?
                </span>
              </h2>
              <p className="text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                Join thousands of developers who&apos;ve discovered the secret to unlimited AI testing.
                Built by a developer who faced your exact problems.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <Link href="/pricing" prefetch={true}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-5 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer"
                  >
                    Start Building Now
                  </motion.div>
                </Link>
                <Link href="/about-developer" prefetch={true}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-white/10 backdrop-blur-sm text-white px-12 py-5 rounded-2xl font-bold text-xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 cursor-pointer"
                  >
                    Meet the Developer
                  </motion.div>
                </Link>
              </div>

              {/* Final stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">∞</div>
                  <div className="text-gray-400">API Requests</div>
                  <div className="text-gray-500 text-sm">No limits, ever</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">300+</div>
                  <div className="text-gray-400">AI Models</div>
                  <div className="text-gray-500 text-sm">All providers</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-bold text-[#ff6b35] mb-2">$0</div>
                  <div className="text-gray-400">Overage Fees</div>
                  <div className="text-gray-500 text-sm">Pay only your API costs</div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

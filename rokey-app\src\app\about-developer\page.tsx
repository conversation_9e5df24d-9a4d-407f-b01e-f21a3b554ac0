'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  BoltIcon,
  CodeBracketIcon,
  CpuChipIcon,
  RocketLaunchIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';

const achievements = [
  { label: "Years of Development", value: "8+" },
  { label: "AI Models Supported", value: "300+" },
  { label: "API Requests Processed", value: "10M+" },
  { label: "Developers Trust RouKey", value: "5,000+" }
];

const principles = [
  {
    icon: BoltIcon,
    title: "Save time and money",
    description: "with every AI request, and focus on building amazing products instead of managing API costs."
  },
  {
    icon: CodeBracketIcon,
    title: "Avoid repetitive tasks",
    description: "Complex routing logic, rate limit handling, provider switching. No more infrastructure headaches."
  },
  {
    icon: RocketLaunchIcon,
    title: "Get unlimited access",
    description: "The more you can test and iterate, the faster you can build and ship. <PERSON>ou<PERSON>ey removes all limits."
  }
];

export default function AboutDeveloperPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      <LandingNavbar />

      {/* Enhanced Grid Background */}
      <EnhancedGridBackground
        gridSize={45}
        opacity={0.06}
        color="#ff6b35"
        variant="premium"
        animated={true}
        className="absolute inset-0"
      />

      <main className="pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              {/* Image Side */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative"
              >
                <div className="relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 overflow-hidden">
                  {/* Background pattern */}
                  <div className="absolute inset-0 opacity-20">
                    <div
                      style={{
                        backgroundImage: `
                          linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                        `,
                        backgroundSize: '20px 20px'
                      }}
                      className="w-full h-full"
                    />
                  </div>
                  
                  <div className="relative z-10">
                    <div className="w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl border-4 border-white/20">
                      <img
                        src="/founder.jpg"
                        alt="Okoro David Chukwunyerem - RouKey Founder"
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                    
                    {/* Name badge */}
                    <div className="mt-6 text-center">
                      <div className="inline-block bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
                        <div className="text-white font-bold text-lg">HEY, I'M OKORO DAVID</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Content Side */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-8"
              >
                <div>
                  <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                    About Me
                  </h1>
                  
                  <div className="space-y-6 text-lg text-gray-300 leading-relaxed">
                    <p>
                      In 2023, I was deep into game development, building AI-powered experiences that required massive amounts of API calls. My monthly bills were hitting <span className="text-[#ff6b35] font-bold">$500+</span>, I was constantly hitting rate limits, and the "smart" routing tools I tried kept failing me.
                    </p>

                    <p>
                      After building <span className="text-[#ff6b35] font-bold">multiple games and apps</span> as a developer, I realized that the biggest bottleneck wasn't creativity or coding skills—it was <span className="text-white font-bold">AI infrastructure costs and limitations</span>. Every developer I knew was facing the same problem: <span className="text-[#ff6b35] font-bold">choose between going broke or building slowly</span>.
                    </p>

                    <p>
                      That's when I had my breakthrough - <span className="text-[#ff6b35] font-bold">intelligent routing between multiple free trial API keys</span> could give essentially unlimited testing access. <span className="text-white font-bold">No more $500 monthly bills for development work!</span>
                    </p>

                    <p>
                      That's why I took my <span className="text-[#ff6b35] font-bold">8+ years of development experience</span> and built <span className="text-white font-bold">RouKey</span> - the AI gateway I wished existed. Six months later, thousands of developers are testing fearlessly and building faster than ever. Why? Because with RouKey, you can:
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Principles Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {principles.map((principle, index) => (
                <motion.div
                  key={principle.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <principle.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {index + 1}. {principle.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {principle.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {achievements.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center group"
                >
                  <div className="text-4xl md:text-5xl font-bold text-[#ff6b35] mb-2 group-hover:scale-110 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="text-gray-300 font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Bottom Quote */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12"
            >
              <p className="text-2xl text-gray-300 leading-relaxed mb-8">
                I built RouKey because I was tired of choosing between going broke or building slowly. <span className="text-[#ff6b35] font-bold">Now you can test fearlessly and build without limits!</span>
              </p>
              
              <div className="flex justify-center">
                <Link href="/pricing">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer"
                  >
                    Start Building with RouKey
                  </motion.div>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

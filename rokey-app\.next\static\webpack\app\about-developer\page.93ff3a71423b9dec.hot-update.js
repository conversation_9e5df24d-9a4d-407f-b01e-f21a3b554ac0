"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about-developer/page",{

/***/ "(app-pages-browser)/./src/app/about-developer/page.tsx":
/*!******************************************!*\
  !*** ./src/app/about-developer/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutDeveloperPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst principles = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Build without fear\",\n        description: \"Test your wildest AI ideas without worrying about costs or rate limits. Innovation thrives when constraints disappear.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Focus on what matters\",\n        description: \"Stop managing API keys and rate limits. Spend your time building amazing products, not infrastructure.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Scale effortlessly\",\n        description: \"From prototype to production, RouKey grows with you. Unlimited requests, 300+ models, zero headaches.\"\n    }\n];\nfunction AboutDeveloperPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                gridSize: 45,\n                opacity: 0.06,\n                color: \"#ff6b35\",\n                variant: \"premium\",\n                animated: true,\n                className: \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        className: \"relative flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 shadow-2xl transform perspective-1000 rotate-y-12\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 opacity-20 rounded-3xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    backgroundImage: \"\\n                            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                          \",\n                                                                    backgroundSize: '20px 20px'\n                                                                },\n                                                                className: \"w-full h-full rounded-3xl\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 69,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 68,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative z-10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"/founder.jpg\",\n                                                                    alt: \"Okoro David Chukwunyerem - RouKey Founder\",\n                                                                    className: \"w-full h-full object-cover\",\n                                                                    loading: \"lazy\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 83,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 81,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-8 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-block bg-gray-800/90 backdrop-blur-sm rounded-2xl px-8 py-4 border border-gray-600/50 shadow-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-bold text-xl tracking-wide\",\n                                                            children: [\n                                                                \"HEY, I'M \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35]\",\n                                                                    children: \"OKORO DAVID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 97,\n                                                                    columnNumber: 34\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.2\n                                        },\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-5xl md:text-6xl font-bold text-white mb-6\",\n                                                    children: \"About Me\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6 text-lg text-gray-300 leading-relaxed\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"In early \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"2025\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 32\n                                                                }, this),\n                                                                \", I was vibe coding and kept hitting rate limits on Gemini free trial plans. It was frustrating - every time I was in the flow, \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"BAM!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 214\n                                                                }, this),\n                                                                \" Rate limit exceeded.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 117,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"So I decided to build a router that would help me use \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"multiple Gemini keys Round Robin style\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 77\n                                                                }, this),\n                                                                \" with automatic fallback so I'd never hit rate limits again. When I saw how well it worked, \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"the idea for RouKey hit me like lightning\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 257\n                                                                }, this),\n                                                                \".\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"I started thinking of ways to make the idea even better and push it to the limits. I had development experience as a \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"game developer\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 140\n                                                                }, this),\n                                                                \" but I had never built a SaaS app - this was going to be a challenge. \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-bold\",\n                                                                    children: \"A challenge I accepted wholly\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 274\n                                                                }, this),\n                                                                \".\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"What started as a simple solution to my own rate limit problem evolved into something much bigger - \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-[#ff6b35] font-bold\",\n                                                                    children: \"the ultimate AI gateway\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 123\n                                                                }, this),\n                                                                \" that gives developers unlimited access to 300+ models. Now thousands of developers can build without limits. Why? Because with RouKey, you can:\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                children: principles.map((principle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(principle.icon, {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-white mb-3\",\n                                                            children: [\n                                                                index + 1,\n                                                                \". \",\n                                                                principle.title\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 leading-relaxed\",\n                                                            children: principle.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, principle.title, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-300 leading-relaxed mb-8\",\n                                        children: [\n                                            \"I built RouKey because I was tired of choosing between going broke or building slowly. \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-[#ff6b35] font-bold\",\n                                                children: \"Now you can test fearlessly and build without limits!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 104\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/pricing\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer\",\n                                                children: \"Start Building with RouKey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutDeveloperPage;\nvar _c;\n$RefreshReg$(_c, \"AboutDeveloperPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about-developer/page.tsx\n"));

/***/ })

});
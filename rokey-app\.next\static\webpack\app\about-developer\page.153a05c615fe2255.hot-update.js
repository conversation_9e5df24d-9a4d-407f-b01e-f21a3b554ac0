"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about-developer/page",{

/***/ "(app-pages-browser)/./src/app/about-developer/page.tsx":
/*!******************************************!*\
  !*** ./src/app/about-developer/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutDeveloperPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,CodeBracketIcon,RocketLaunchIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst principles = [\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Get intelligent routing\",\n        description: \"Not just any model - the RIGHT model for each task. Smart algorithms match your requests to optimal AI models automatically.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Build with role-based logic\",\n        description: \"Define custom roles and let RouKey intelligently route based on context. Coding, writing, analysis - each gets the perfect model.\"\n    },\n    {\n        icon: _barrel_optimize_names_BoltIcon_CodeBracketIcon_RocketLaunchIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        title: \"Scale without limits\",\n        description: \"Unlimited requests across 300+ models with intelligent failover. Your AI infrastructure that actually thinks.\"\n    }\n];\nfunction AboutDeveloperPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"b9f7c4426befc960\",\n                children: \".perspective-1000.jsx-b9f7c4426befc960{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.rotate-y-12.jsx-b9f7c4426befc960{-webkit-transform:rotatey(-12deg)rotatex(5deg);-moz-transform:rotatey(-12deg)rotatex(5deg);-ms-transform:rotatey(-12deg)rotatex(5deg);-o-transform:rotatey(-12deg)rotatex(5deg);transform:rotatey(-12deg)rotatex(5deg);-webkit-transition:-webkit-transform.3s ease;-moz-transition:-moz-transform.3s ease;-o-transition:-o-transform.3s ease;transition:-webkit-transform.3s ease;transition:-moz-transform.3s ease;transition:-o-transform.3s ease;transition:transform.3s ease}.rotate-y-12.jsx-b9f7c4426befc960:hover{-webkit-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-moz-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-ms-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-o-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);transform:rotatey(-8deg)rotatex(2deg)scale(1.02)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-b9f7c4426befc960\" + \" \" + \"min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        gridSize: 45,\n                        opacity: 0.06,\n                        color: \"#ff6b35\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"pt-20 relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"py-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                className: \"relative flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 shadow-2xl transform perspective-1000 rotate-y-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"absolute inset-0 opacity-20 rounded-3xl\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundImage: \"\\n                            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                          \",\n                                                                            backgroundSize: '20px 20px'\n                                                                        },\n                                                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"w-full h-full rounded-3xl\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                        lineNumber: 82,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 81,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"relative z-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: \"/founder.jpg\",\n                                                                            alt: \"Okoro David Chukwunyerem - RouKey Founder\",\n                                                                            loading: \"lazy\",\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"w-full h-full object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 96,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                        lineNumber: 95,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 94,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"mt-8 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"inline-block bg-gray-800/90 backdrop-blur-sm rounded-2xl px-8 py-4 border border-gray-600/50 shadow-xl\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-white font-bold text-xl tracking-wide\",\n                                                                    children: [\n                                                                        \"HEY, I'M \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35]\",\n                                                                            children: \"OKORO DAVID\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 110,\n                                                                            columnNumber: 34\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.2\n                                                },\n                                                className: \"space-y-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b9f7c4426befc960\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-5xl md:text-6xl font-bold text-white mb-6\",\n                                                            children: [\n                                                                \"From Rate Limits to\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] block\",\n                                                                    children: \"Unlimited AI\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"space-y-6 text-lg text-gray-300 leading-relaxed\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-b9f7c4426befc960\",\n                                                                    children: [\n                                                                        \"In early \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] font-bold\",\n                                                                            children: \"2025\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 32\n                                                                        }, this),\n                                                                        \", I was deep in a coding session when I kept hitting rate limits on Gemini. Every time I got into the flow, \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-white font-bold\",\n                                                                            children: \"boom - rate limit exceeded\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 194\n                                                                        }, this),\n                                                                        \". It was killing my productivity.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 131,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-b9f7c4426befc960\",\n                                                                    children: [\n                                                                        \"I started with a simple Round Robin router, but then I realized something bigger: \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-white font-bold\",\n                                                                            children: \"what if routing could be intelligent?\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 136,\n                                                                            columnNumber: 105\n                                                                        }, this),\n                                                                        \" What if it could automatically choose the \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] font-bold\",\n                                                                            children: \"best model for each specific task\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 136,\n                                                                            columnNumber: 231\n                                                                        }, this),\n                                                                        \" - routing coding questions to Claude, creative tasks to GPT, and analysis to specialized models?\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 135,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-b9f7c4426befc960\",\n                                                                    children: [\n                                                                        \"That's when I knew I wasn't just solving rate limits - I was building the future of AI routing. With my experience in \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] font-bold\",\n                                                                            children: \"complex systems and game logic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 141\n                                                                        }, this),\n                                                                        \", I developed smart algorithms that understand context, roles, and optimal model selection.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-b9f7c4426befc960\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] font-bold\",\n                                                                            children: \"RouKey\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 144,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        \" became the world's first truly intelligent AI gateway - not just cycling through models, but \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-white font-bold\",\n                                                                            children: \"intelligently matching each request to the perfect model\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                            lineNumber: 144,\n                                                                            columnNumber: 173\n                                                                        }, this),\n                                                                        \". Now thousands of developers get better results, faster responses, and unlimited access. Why? Because with RouKey, you can:\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 143,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"py-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                        children: principles.map((principle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.6,\n                                                    delay: index * 0.1\n                                                },\n                                                className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b9f7c4426befc960\" + \" \" + \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(principle.icon, {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-b9f7c4426befc960\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-xl font-bold text-white mb-3\",\n                                                                    children: [\n                                                                        index + 1,\n                                                                        \". \",\n                                                                        principle.title\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-gray-300 leading-relaxed\",\n                                                                    children: principle.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, principle.title, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"py-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-b9f7c4426befc960\" + \" \" + \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        className: \"bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-2xl text-gray-300 leading-relaxed mb-8\",\n                                                children: [\n                                                    \"What started as a simple fix for my own rate limit frustration became the tool that \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-[#ff6b35] font-bold\",\n                                                        children: \"unlocks unlimited AI potential\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 101\n                                                    }, this),\n                                                    \" for thousands of developers. \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-b9f7c4426befc960\" + \" \" + \"text-white font-bold\",\n                                                        children: \"Your next breakthrough is just one API call away!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 211\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-b9f7c4426befc960\" + \" \" + \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/pricing\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                        whileHover: {\n                                                            scale: 1.05\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer\",\n                                                        children: \"Start Building with RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_c = AboutDeveloperPage;\nvar _c;\n$RefreshReg$(_c, \"AboutDeveloperPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/about-developer/page.tsx\n"));

/***/ })

});